// API service for communicating with the backend
const API_BASE_URL = '/api'; // This will be proxied to http://localhost:8080/api

export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export interface HelloResponse {
  message: string;
  timestamp: string;
}

export interface StatusResponse {
  status: string;
  version: string;
  uptime: number;
}

export interface EchoResponse {
  message: string;
  received: any;
  timestamp: string;
}

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('API request failed:', error);
      return { 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  async getHello(): Promise<ApiResponse<HelloResponse>> {
    return this.request<HelloResponse>('/hello');
  }

  async getStatus(): Promise<ApiResponse<StatusResponse>> {
    return this.request<StatusResponse>('/status');
  }

  async postEcho(data: any): Promise<ApiResponse<EchoResponse>> {
    return this.request<EchoResponse>('/echo', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

export const apiService = new ApiService();
