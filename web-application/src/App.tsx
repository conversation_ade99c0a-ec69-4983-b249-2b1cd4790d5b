import { useState, useEffect } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import { apiService, HelloResponse, StatusResponse, EchoResponse } from './services/api'

function App() {
  const [count, setCount] = useState(0)
  const [helloData, setHelloData] = useState<HelloResponse | null>(null)
  const [statusData, setStatusData] = useState<StatusResponse | null>(null)
  const [echoData, setEchoData] = useState<EchoResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchHello = async () => {
    setLoading(true)
    setError(null)
    const response = await apiService.getHello()
    if (response.error) {
      setError(response.error)
    } else {
      setHelloData(response.data || null)
    }
    setLoading(false)
  }

  const fetchStatus = async () => {
    setLoading(true)
    setError(null)
    const response = await apiService.getStatus()
    if (response.error) {
      setError(response.error)
    } else {
      setStatusData(response.data || null)
    }
    setLoading(false)
  }

  const sendEcho = async () => {
    setLoading(true)
    setError(null)
    const testData = { message: 'Hello from frontend!', count }
    const response = await apiService.postEcho(testData)
    if (response.error) {
      setError(response.error)
    } else {
      setEchoData(response.data || null)
    }
    setLoading(false)
  }

  useEffect(() => {
    // Fetch initial data when component mounts
    fetchHello()
    fetchStatus()
  }, [])

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>React + Node.js Connection Demo</h1>

      {error && (
        <div style={{ color: 'red', margin: '20px 0' }}>
          Error: {error}
        </div>
      )}

      {loading && <div>Loading...</div>}

      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>

        <div style={{ margin: '20px 0' }}>
          <h3>Backend API Tests:</h3>

          <div style={{ margin: '10px 0' }}>
            <button onClick={fetchHello} disabled={loading}>
              Fetch Hello
            </button>
            {helloData && (
              <div style={{ marginTop: '10px', padding: '10px', background: '#f0f0f0' }}>
                <strong>Hello Response:</strong><br />
                Message: {helloData.message}<br />
                Timestamp: {helloData.timestamp}
              </div>
            )}
          </div>

          <div style={{ margin: '10px 0' }}>
            <button onClick={fetchStatus} disabled={loading}>
              Fetch Status
            </button>
            {statusData && (
              <div style={{ marginTop: '10px', padding: '10px', background: '#f0f0f0' }}>
                <strong>Status Response:</strong><br />
                Status: {statusData.status}<br />
                Version: {statusData.version}<br />
                Uptime: {Math.round(statusData.uptime)}s
              </div>
            )}
          </div>

          <div style={{ margin: '10px 0' }}>
            <button onClick={sendEcho} disabled={loading}>
              Send Echo (POST)
            </button>
            {echoData && (
              <div style={{ marginTop: '10px', padding: '10px', background: '#f0f0f0' }}>
                <strong>Echo Response:</strong><br />
                Message: {echoData.message}<br />
                Received: {JSON.stringify(echoData.received)}<br />
                Timestamp: {echoData.timestamp}
              </div>
            )}
          </div>
        </div>
      </div>

      <p className="read-the-docs">
        The buttons above demonstrate GET and POST requests to your Node.js backend
      </p>
    </>
  )
}

export default App
